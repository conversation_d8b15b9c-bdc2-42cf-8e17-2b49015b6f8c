<!DOCTYPE html><html><head><style data-next-hide-fouc="true">body{display:none}</style><noscript data-next-hide-fouc="true"><style>body{display:block}</style></noscript><meta charSet="utf-8"/><meta name="viewport" content="width=device-width"/><meta name="next-head-count" content="2"/><noscript data-n-css=""></noscript><script defer="" nomodule="" src="/_next/static/chunks/polyfills.js"></script><script src="/_next/static/chunks/fallback/webpack.js" defer=""></script><script src="/_next/static/chunks/fallback/main.js" defer=""></script><script src="/_next/static/chunks/fallback/pages/_app.js" defer=""></script><script src="/_next/static/chunks/fallback/pages/_error.js" defer=""></script><noscript id="__next_css__DO_NOT_USE__"></noscript></head><body><div id="__next"></div><script src="/_next/static/chunks/fallback/react-refresh.js"></script><script id="__NEXT_DATA__" type="application/json">{"props":{"pageProps":{"statusCode":500}},"page":"/_error","query":{},"buildId":"development","isFallback":false,"err":{"name":"ModuleBuildError","source":"server","message":"Module build failed (from ./node_modules/next/dist/build/babel/loader/index.js):\nError: ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\babel.config.json'\n    at readFileSync (node:fs:448:20)\n    at getCustomBabelConfig (C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\build\\babel\\loader\\get-config.js:151:53)\n    at Object.getFreshConfig (C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\build\\babel\\loader\\get-config.js:221:37)\n    at Object.getConfig (C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\build\\babel\\loader\\get-config.js:330:40)\n    at Object.transform (C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\build\\babel\\loader\\transform.js:74:44)\n    at C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\build\\babel\\loader\\index.js:23:110\n    at Span.traceFn (C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\trace\\trace.js:147:20)\n    at Object.nextBabelLoader (C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\build\\babel\\loader\\index.js:23:79)\n    at C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\build\\babel\\loader\\index.js:32:49\n    at Span.traceAsyncFn (C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\trace\\trace.js:154:26)\n    at Object.nextBabelLoaderOuter (C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\build\\babel\\loader\\index.js:32:16)","stack":"ModuleBuildError: Module build failed (from ./node_modules/next/dist/build/babel/loader/index.js):\nError: ENOENT: no such file or directory, open 'C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\babel.config.json'\n    at readFileSync (node:fs:448:20)\n    at getCustomBabelConfig (C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\build\\babel\\loader\\get-config.js:151:53)\n    at Object.getFreshConfig (C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\build\\babel\\loader\\get-config.js:221:37)\n    at Object.getConfig (C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\build\\babel\\loader\\get-config.js:330:40)\n    at Object.transform (C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\build\\babel\\loader\\transform.js:74:44)\n    at C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\build\\babel\\loader\\index.js:23:110\n    at Span.traceFn (C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\trace\\trace.js:147:20)\n    at Object.nextBabelLoader (C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\build\\babel\\loader\\index.js:23:79)\n    at C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\build\\babel\\loader\\index.js:32:49\n    at Span.traceAsyncFn (C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\trace\\trace.js:154:26)\n    at Object.nextBabelLoaderOuter (C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\build\\babel\\loader\\index.js:32:16)\n    at processResult (C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:28:400590)\n    at C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\compiled\\webpack\\bundle5.js:28:402302\n    at C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\compiled\\loader-runner\\LoaderRunner.js:1:8645\n    at C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\compiled\\loader-runner\\LoaderRunner.js:1:5828\n    at r.callback (C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\compiled\\loader-runner\\LoaderRunner.js:1:4039)\n    at C:\\Users\\<USER>\\OneDrive\\Documents\\VSCODE Project\\website-ocean-soul-sparkles\\node_modules\\next\\dist\\build\\babel\\loader\\index.js:33:37"},"gip":true,"scriptLoader":[]}</script></body></html>